"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Anchor, FileText, Home, LifeBuoy, Menu, Settings } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

interface AppSidebarProps {
  isCollapsed: boolean;
  onCollapseChange: (isCollapsed: boolean) => void;
}

export function AppSidebar({ isCollapsed, onCollapseChange }: AppSidebarProps) {
  const pathname = usePathname();

  const toggleCollapse = () => {
    onCollapseChange(!isCollapsed);
  };

  const isActive = (path: string) => {
    return pathname === path || pathname?.startsWith(path + "/");
  };

  const mainNavItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: Home,
    },
    {
      name: "Certificates",
      href: "/certificates",
      icon: FileText,
    },
  ];

  const bottomNavItems = [
    {
      name: "Settings",
      href: "/settings",
      icon: Settings,
    },
    {
      name: "Help & Support",
      href: "/help",
      icon: LifeBuoy,
    },
  ];

  // Mobile sidebar content
  const sidebarContent = (
    <>
      <div className="flex items-center gap-2 px-4 py-3 border-b">
        <Anchor className="h-6 w-6 text-primary" />
        <span className="text-xl font-bold">Sealog</span>
      </div>

      <div className="space-y-1 px-3 py-4">
        <p className="px-2 text-xs font-medium text-muted-foreground mb-2">
          NAVIGATION
        </p>
        {mainNavItems.map((item) => (
          <Button
            key={item.name}
            variant={isActive(item.href) ? "secondary" : "ghost"}
            size="sm"
            className="w-full justify-start gap-2 relative"
            asChild
          >
            <Link href={item.href}>
              <item.icon className="h-4 w-4" />
              {item.name}
            </Link>
          </Button>
        ))}
      </div>

      <div className="mt-auto px-3 py-4 border-t">
        {bottomNavItems.map((item) => (
          <Button
            key={item.name}
            variant={isActive(item.href) ? "secondary" : "ghost"}
            size="sm"
            className="w-full justify-start gap-2 mb-1"
            asChild
          >
            <Link href={item.href}>
              <item.icon className="h-4 w-4" />
              {item.name}
            </Link>
          </Button>
        ))}
      </div>
    </>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <div
        className={`hidden md:flex flex-col border-r bg-background h-full transition-all duration-300 ${
          isCollapsed ? "w-16" : "w-64"
        }`}
      >
        <div className="flex items-center justify-between h-16 border-b px-4">
          {!isCollapsed ? (
            <>
              <div className="flex items-center gap-2">
                <Anchor className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">Sealog</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9"
                onClick={toggleCollapse}
              >
                <Menu className="h-4 w-4" />
              </Button>
            </>
          ) : (
            <div className="flex justify-center w-full">
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9"
                onClick={toggleCollapse}
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {isCollapsed ? (
          <>
            <div className="flex flex-col items-center gap-4 py-4">
              {mainNavItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`p-2 rounded-md ${
                    isActive(item.href)
                      ? "bg-secondary"
                      : "hover:bg-secondary/50"
                  }`}
                  title={item.name}
                >
                  <item.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>
            <div className="mt-auto flex flex-col items-center gap-4 py-4 border-t">
              {bottomNavItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`p-2 rounded-md ${
                    isActive(item.href)
                      ? "bg-secondary"
                      : "hover:bg-secondary/50"
                  }`}
                  title={item.name}
                >
                  <item.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>
          </>
        ) : (
          <>
            <div className="space-y-1 px-3 py-4">
              <p className="px-2 text-xs font-medium text-muted-foreground mb-2">
                NAVIGATION
              </p>
              {mainNavItems.map((item) => (
                <Button
                  key={item.name}
                  variant={isActive(item.href) ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start gap-2 relative"
                  asChild
                >
                  <Link href={item.href}>
                    <item.icon className="h-4 w-4" />
                    {item.name}
                  </Link>
                </Button>
              ))}
            </div>

            <div className="mt-auto px-3 py-4 border-t">
              {bottomNavItems.map((item) => (
                <Button
                  key={item.name}
                  variant={isActive(item.href) ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start gap-2 mb-1"
                  asChild
                >
                  <Link href={item.href}>
                    <item.icon className="h-4 w-4" />
                    {item.name}
                  </Link>
                </Button>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Mobile Sidebar */}
      <div className="md:hidden">
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="fixed top-4 left-4 z-40"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-64 p-0">
            <div className="flex h-full flex-col">{sidebarContent}</div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}
