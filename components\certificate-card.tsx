import {
  Eye,
  Download,
  Star,
  Calendar,
  Award,
  Clock,
  Edit,
  Trash2,
  Share2,
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  <PERSON>T<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useState } from "react";

interface CertificateCardProps {
  cert: {
    id: string;
    name: string;
    certificateNumber: string;
    dateIssued: Date;
    expiryDate: Date;
    isFavorite?: boolean;
    status: "active" | "expired" | "expiring-soon";
    issuingAuthority: string;
  };
  onView: (id: string) => void;
}

export function CertificateCard({ cert, onView }: CertificateCardProps) {
  const [isFavorite, setIsFavorite] = useState(cert.isFavorite || false);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Active
          </Badge>
        );
      case "expiring-soon":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Expiring Soon
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Expired
          </Badge>
        );
      default:
        return null;
    }
  };

  const getDaysUntilExpiry = () => {
    const today = new Date();
    const expiryDate = new Date(cert.expiryDate);
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysRemaining = getDaysUntilExpiry();

  const toggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    // In a real app, you would update this in your database
  };

  return (
    <TooltipProvider>
      <Card className="hover:shadow-md transition-shadow group overflow-hidden border-2 hover:border-primary/20 active:scale-[0.98]">
        <CardHeader className="pb-2 relative p-4 sm:p-6">
          <div className="absolute right-3 top-3 sm:right-4 sm:top-4 flex gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 sm:h-8 sm:w-8 hover:text-yellow-500"
                  onClick={toggleFavorite}
                >
                  <Star
                    className={`h-4 w-4 ${
                      isFavorite ? "text-yellow-500 fill-yellow-500" : ""
                    }`}
                  />
                  <span className="sr-only">Favorite</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isFavorite ? "Remove from favorites" : "Add to favorites"}
              </TooltipContent>
            </Tooltip>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 sm:h-8 sm:w-8"
                >
                  <span className="sr-only">Open menu</span>
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                    />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onView(cert.id)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <CardTitle className="text-base sm:text-lg pr-16 sm:pr-20 leading-tight">
            {cert.name}
          </CardTitle>
          <div className="flex justify-between items-center mt-2">
            <span className="text-xs sm:text-sm text-muted-foreground flex items-center">
              <Award className="h-3.5 w-3.5 mr-1 inline flex-shrink-0" />
              <span className="truncate">{cert.certificateNumber}</span>
            </span>
            {getStatusBadge(cert.status)}
          </div>
        </CardHeader>

        <CardContent className="p-4 sm:p-6">
          <div className="text-sm space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center">
                <Calendar className="h-3.5 w-3.5 mr-1" />
                Issued:
              </span>
              <span>{formatDate(cert.dateIssued)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center">
                <Clock className="h-3.5 w-3.5 mr-1" />
                Expires:
              </span>
              <span
                className={
                  cert.status === "expired" ? "text-red-600 font-medium" : ""
                }
              >
                {formatDate(cert.expiryDate)}
              </span>
            </div>
            {cert.status !== "expired" && (
              <div className="mt-2 text-xs">
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full ${
                      cert.status === "expiring-soon"
                        ? "bg-yellow-500"
                        : "bg-green-500"
                    }`}
                    style={{
                      width: `${Math.min(
                        100,
                        Math.max(0, (daysRemaining / 365) * 100)
                      )}%`,
                    }}
                  ></div>
                </div>
                <div className="flex justify-between mt-1">
                  <span
                    className={`${
                      cert.status === "expiring-soon"
                        ? "text-yellow-700"
                        : "text-green-700"
                    }`}
                  >
                    {daysRemaining > 0
                      ? `${daysRemaining} days remaining`
                      : "Expired"}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="pt-0 pb-4 sm:pb-6 px-4 sm:px-6 flex justify-between gap-2">
          <Button
            variant="outline"
            size="default"
            className="h-10 flex-1"
            onClick={() => onView(cert.id)}
          >
            <Eye className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">View Details</span>
            <span className="xs:hidden">View</span>
          </Button>
          <Button
            variant="ghost"
            size="default"
            className="h-10 flex-1 text-muted-foreground"
          >
            <Download className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">Export</span>
            <span className="xs:hidden">Export</span>
          </Button>
        </CardFooter>
      </Card>
    </TooltipProvider>
  );
}
