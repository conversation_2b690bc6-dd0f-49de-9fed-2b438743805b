"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import { AppTopbar } from "@/components/app-topbar"

export default function AppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)

  useEffect(() => {
    // Check if the session cookie exists
    const hasCookie = document.cookie.split(";").some((item) => item.trim().startsWith("session="))

    if (!hasCookie) {
      router.push("/login-alt")
      return
    }

    setIsAuthenticated(true)
    setIsLoading(false)
  }, [router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Don't render anything while redirecting
  }

  return (
    <div className="flex min-h-screen">
      {/* Sidebar - Fixed position */}
      <div className={`fixed left-0 top-0 h-screen z-30 transition-all duration-300 ${
        isSidebarCollapsed ? 'w-16' : 'w-64'
      }`}>
        <AppSidebar 
          isCollapsed={isSidebarCollapsed} 
          onCollapseChange={setIsSidebarCollapsed} 
        />
      </div>

      {/* Main content area */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${
        isSidebarCollapsed ? 'ml-16 w-[calc(100%-4rem)]' : 'ml-64 w-[calc(100%-16rem)]'
      }`}>
        {/* Topbar */}
        <header className="sticky top-0 z-20 h-16 border-b bg-background px-4 md:px-6">
          <AppTopbar />
        </header>
        
        {/* Page content */}
        <main className="flex-1 w-full">
          {children}
        </main>
      </div>
    </div>
  )
}
