"use client";

import Link from "next/link";
import {
  Bell,
  HelpCircle,
  LogOut,
  Plus,
  Search,
  Settings,
  User,
} from "lucide-react";
import { usePathname } from "next/navigation";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export function AppTopbar() {
  const pathname = usePathname();

  // Function to get the current page title based on the pathname
  const getPageTitle = () => {
    if (pathname === "/dashboard") return "Dashboard";
    if (pathname === "/certificates") return "Certificates";
    if (pathname?.startsWith("/certificates/new")) return "Add New Certificate";
    if (pathname?.startsWith("/certificates/")) return "Certificate Details";
    if (pathname === "/profile") return "My Profile";
    if (pathname === "/settings") return "Settings";
    return "Sealog";
  };

  const handleLogout = () => {
    // Clear the session cookie
    document.cookie = "session=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT";
    window.location.href = "/login-alt";
  };

  // Sample notifications for the dropdown
  const notifications = [
    {
      id: "1",
      title: "Certificate Expiring Soon",
      message: "Your GMDSS Radio Operator certificate will expire in 15 days.",
      time: "1 day ago",
    },
    {
      id: "2",
      title: "Certificate Added",
      message:
        "Your STCW Basic Safety Training certificate has been added successfully.",
      time: "2 days ago",
    },
    {
      id: "3",
      title: "Certificate Expired",
      message: "Your Ship Security Officer certificate has expired.",
      time: "3 days ago",
    },
  ];

  return (
    <div className="flex h-16 items-center gap-2 sm:gap-4 w-full">
      {/* Mobile: Add left padding to account for mobile menu button */}
      <div className="text-lg font-semibold ml-12 md:ml-0 truncate">
        {getPageTitle()}
      </div>

      <div className="ml-auto flex items-center gap-1 sm:gap-2 md:gap-4">
        {/* Mobile Search - Hidden on very small screens, shown as icon on small screens */}
        <div className="hidden sm:flex md:hidden">
          <Button variant="ghost" size="icon" className="h-9 w-9">
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>
        </div>

        {/* Desktop Search */}
        <div className="hidden md:flex relative w-40 lg:w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search..."
            className="w-full pl-8 md:w-40 lg:w-64"
          />
        </div>

        {/* Add Certificate Button - Responsive */}
        <Button
          variant="outline"
          size="sm"
          className="hidden sm:flex gap-1"
          asChild
        >
          <Link href="/certificates/new">
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline">Add Certificate</span>
            <span className="md:hidden">Add</span>
          </Link>
        </Button>

        {/* Mobile Add Button - Icon only for very small screens */}
        <Button
          variant="outline"
          size="icon"
          className="sm:hidden h-9 w-9"
          asChild
        >
          <Link href="/certificates/new">
            <Plus className="h-4 w-4" />
            <span className="sr-only">Add Certificate</span>
          </Link>
        </Button>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="relative h-9 w-9">
              <Bell className="h-4 w-4" />
              <Badge className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs">
                3
              </Badge>
              <span className="sr-only">Notifications</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent align="end" className="w-80 sm:w-96 p-0">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">Notifications</h4>
                <Link
                  href="/notifications"
                  className="text-xs text-primary hover:underline"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="max-h-80 overflow-auto">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className="p-4 border-b hover:bg-muted/50"
                >
                  <div className="flex justify-between items-start mb-1">
                    <h5 className="font-medium text-sm">
                      {notification.title}
                    </h5>
                    <span className="text-xs text-muted-foreground">
                      {notification.time}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {notification.message}
                  </p>
                </div>
              ))}
            </div>
          </PopoverContent>
        </Popover>

        <Button
          variant="ghost"
          size="icon"
          className="hidden sm:flex h-9 w-9"
          asChild
        >
          <Link href="/help">
            <HelpCircle className="h-4 w-4" />
            <span className="sr-only">Help</span>
          </Link>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full h-9 w-9"
            >
              <Avatar className="h-7 w-7">
                <AvatarImage
                  src="/placeholder.svg?height=32&width=32"
                  alt="User"
                />
                <AvatarFallback className="text-xs">JS</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  Captain John Smith
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  <EMAIL>
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile" className="cursor-pointer">
                <User className="mr-2 h-4 w-4" /> My Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="cursor-pointer">
                <Settings className="mr-2 h-4 w-4" /> Account Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleLogout}
              className="text-red-500 focus:text-red-500 cursor-pointer"
            >
              <LogOut className="mr-2 h-4 w-4" /> Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
