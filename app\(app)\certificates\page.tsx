"use client";

import { useMemo, useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Download,
  FileText,
  Plus,
  Search,
  SlidersHorizontal,
  Star,
  Clock,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CertificateCard } from "@/components/certificate-card";
import { CertificateTable } from "@/components/certificate-table";
import { CertificateStatusBadge } from "@/components/certificate-status-badge";
import { DeleteCertificateButton } from "@/components/delete-certificate-button";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Certificate type definition
type Certificate = {
  id: string;
  name: string;
  issuingAuthority: string;
  certificateNumber: string;
  dateIssued: Date;
  expiryDate: Date;
  isFavorite?: boolean;
  status: "active" | "expired" | "expiring-soon";
};

export default function CertificatesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // URL-based state management
  const query = searchParams?.get("query") || "";
  const urlView = searchParams?.get("view") || "grid";
  const urlFilter = searchParams?.get("filter") || "all";
  const urlSort = searchParams?.get("sort") || "expiryDate";
  const urlOrder = searchParams?.get("order") || "asc";

  // Component state
  const [view, setView] = useState<"grid" | "table">(
    urlView as "grid" | "table"
  );
  const [activeFilter, setActiveFilter] = useState<string>(urlFilter);
  const [searchQuery, setSearchQuery] = useState(query);
  const [sortBy, setSortBy] = useState<"name" | "dateIssued" | "expiryDate">(
    urlSort as any
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">(
    urlOrder as "asc" | "desc"
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificates, setCertificates] = useState<Certificate[]>([]);

  const handleViewCertificate = (certificateId: string) => {
    router.push(`/certificates/${certificateId}`);
  };

  useEffect(() => {
    // Check if the session cookie exists
    const hasCookie = document.cookie
      .split(";")
      .some((item) => item.trim().startsWith("session="));

    if (!hasCookie) {
      router.push("/login-alt");
      return;
    }

    try {
      // Enhanced certificate data with status and favorites
      const certificatesData: Certificate[] = [
        {
          id: "1",
          name: "STCW Basic Safety Training",
          issuingAuthority: "Maritime Safety Authority",
          certificateNumber: "BST-2023-12345",
          dateIssued: new Date("2023-01-15"),
          expiryDate: new Date("2025-01-15"),
          isFavorite: true,
          status: "active",
        },
        {
          id: "2",
          name: "Medical First Aid",
          issuingAuthority: "Maritime Medical Institute",
          certificateNumber: "MFA-2022-54321",
          dateIssued: new Date("2022-08-22"),
          expiryDate: new Date("2024-08-22"),
          status: "active",
        },
        {
          id: "3",
          name: "Proficiency in Survival Craft",
          issuingAuthority: "Nautical Training Center",
          certificateNumber: "PSC-2023-98765",
          dateIssued: new Date("2023-03-22"),
          expiryDate: new Date("2023-12-31"),
          status: "expiring-soon",
        },
        {
          id: "4",
          name: "Advanced Firefighting",
          issuingAuthority: "Maritime Safety Authority",
          certificateNumber: "AFF-2021-45678",
          dateIssued: new Date("2021-09-05"),
          expiryDate: new Date("2023-09-05"),
          status: "expired",
        },
        {
          id: "5",
          name: "GMDSS Radio Operator",
          issuingAuthority: "Communications Authority",
          certificateNumber: "GMDSS-2021-87654",
          dateIssued: new Date("2021-06-15"),
          expiryDate: new Date("2024-06-15"),
          isFavorite: true,
          status: "active",
        },
        {
          id: "6",
          name: "Ship Security Officer",
          issuingAuthority: "Maritime Security Institute",
          certificateNumber: "SSO-2022-13579",
          dateIssued: new Date("2022-05-10"),
          expiryDate: new Date("2024-05-10"),
          status: "active",
        },
      ];

      setCertificates(certificatesData);
      setIsLoading(false);
    } catch (err) {
      console.error("Error loading certificates:", err);
      setError("Failed to load certificates. Please try again.");
      setIsLoading(false);
    }
  }, [router]);

  // Enhanced filtering and sorting logic
  const filteredAndSortedCertificates = useMemo(() => {
    // First filter the certificates
    const filtered = certificates.filter((cert) => {
      const matchesFilter =
        activeFilter === "favorites"
          ? cert.isFavorite
          : activeFilter === "expiring-soon"
          ? cert.status === "expiring-soon"
          : activeFilter === "expired"
          ? cert.status === "expired"
          : true;

      const matchesSearch =
        cert.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cert.certificateNumber
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        cert.issuingAuthority.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesFilter && (searchQuery === "" || matchesSearch);
    });

    // Then sort them
    return [...filtered].sort((a, b) => {
      let comparison = 0;

      if (sortBy === "name") {
        comparison = a.name.localeCompare(b.name);
      } else if (sortBy === "dateIssued") {
        comparison =
          new Date(a.dateIssued).getTime() - new Date(b.dateIssued).getTime();
      } else if (sortBy === "expiryDate") {
        comparison =
          new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });
  }, [certificates, activeFilter, searchQuery, sortBy, sortOrder]);

  const actionCards = [
    {
      title: "All",
      description: "View all certificates",
      icon: FileText,
      count: certificates.length,
      onClick: () => setActiveFilter("all"),
      variant: "outline" as const,
    },
    {
      title: "Favorites",
      description: "View your favorite certificates",
      icon: Star,
      count: certificates.filter((c) => c.isFavorite).length,
      onClick: () => setActiveFilter("favorites"),
      variant: "outline" as const,
    },
    {
      title: "Expiring Soon",
      description: "Certificates that need attention",
      icon: Clock,
      count: certificates.filter((c) => c.status === "expiring-soon").length,
      onClick: () => setActiveFilter("expiring-soon"),
      variant: "outline" as const,
    },
    {
      title: "Expired",
      description: "View expired certificates",
      icon: AlertCircle,
      count: certificates.filter((c) => c.status === "expired").length,
      onClick: () => setActiveFilter("expired"),
      variant: "outline" as const,
    },
  ];

  const formatDate = (date: Date | null) => {
    if (!date) return "No Expiry";
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDaysRemaining = (expiryDate: Date | null) => {
    if (!expiryDate) return null;
    const today = new Date();
    const diffTime = new Date(expiryDate).getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDaysRemainingText = (expiryDate: Date | null) => {
    if (!expiryDate) return "No Expiry";
    const days = getDaysRemaining(expiryDate);
    if (!days) return "No Expiry";
    if (days < 0) return `Expired ${Math.abs(days)} days ago`;
    if (days === 0) return "Expires today";
    return `${days} days remaining`;
  };

  const getDaysRemainingClass = (expiryDate: Date | null) => {
    if (!expiryDate) return "text-gray-500";
    const days = getDaysRemaining(expiryDate);
    if (!days) return "text-gray-500";
    if (days < 0) return "text-red-500";
    if (days <= 30) return "text-orange-500";
    if (days <= 90) return "text-yellow-500";
    return "text-green-500";
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading certificates...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Certificates</h1>
          <p className="text-muted-foreground">
            Manage your professional certificates and documents
          </p>
        </div>
        <div className="flex gap-2 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search certificates..."
              className="w-full pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="sr-only">Sort options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Sort by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup
                value={sortBy}
                onValueChange={(value) => setSortBy(value as any)}
              >
                <DropdownMenuRadioItem value="name">Name</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="dateIssued">
                  Date Issued
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="expiryDate">
                  Expiry Date
                </DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup
                value={sortOrder}
                onValueChange={(value) => setSortOrder(value as any)}
              >
                <DropdownMenuRadioItem value="asc">
                  Ascending
                </DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="desc">
                  Descending
                </DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button asChild>
            <Link href="/certificates/new">
              <Plus className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Add Certificate</span>
              <span className="sm:hidden">Add</span>
            </Link>
          </Button>
        </div>
      </div>

      {/* Action Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {actionCards.map((card, index) => (
          <Card
            key={index}
            className={`cursor-pointer transition-all hover:shadow-md ${
              (activeFilter === "all" && card.title === "All") ||
              (activeFilter === "favorites" && card.title === "Favorites") ||
              (activeFilter === "expiring-soon" &&
                card.title === "Expiring Soon") ||
              (activeFilter === "expired" && card.title === "Expired")
                ? "ring-2 ring-primary"
                : ""
            }`}
            onClick={card.onClick}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {card.count !== undefined ? card.count : ""}
              </div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Certificates List */}
      <Card>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-6 pb-0">
          <div className="flex items-center space-x-2">
            <Tabs
              value={view}
              onValueChange={(v) => setView(v as "grid" | "table")}
              className="w-full"
            >
              <TabsList>
                <TabsTrigger value="grid">Grid</TabsTrigger>
                <TabsTrigger value="table">Table</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        <div className="p-6">
          {view === "table" ? (
            <CertificateTable
              certificates={filteredAndSortedCertificates}
              onView={handleViewCertificate}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAndSortedCertificates.map((cert) => (
                <CertificateCard
                  key={cert.id}
                  cert={cert}
                  onView={handleViewCertificate}
                />
              ))}
            </div>
          )}

          {filteredAndSortedCertificates.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No certificates found</h3>
              <p className="text-sm text-muted-foreground mb-4">
                {activeFilter === "all"
                  ? "Get started by adding a new certificate."
                  : "No certificates match the selected filter."}
              </p>
              {activeFilter !== "all" && (
                <Button
                  variant="outline"
                  onClick={() => setActiveFilter("all")}
                >
                  Clear filters
                </Button>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
